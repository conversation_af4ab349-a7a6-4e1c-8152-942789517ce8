<mxfile host="app.diagrams.net" agent="Augment Agent" version="24.7.10" type="device">
  <diagram id="ods-dwd-detail-etl" name="ODS→DWD详细ETL流程">
    <mxGraphModel dx="2200" dy="1400" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2800" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>

        <!-- 标题区域 -->
        <mxCell id="title" value="通用VOC报表系统 - ODS到DWD层详细ETL流程图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;fontColor=#2c3e50;" vertex="1" parent="1">
          <mxGeometry x="800" y="20" width="800" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="subtitle" value="配置驱动的多行业数据建模与智能化ETL处理流程" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=0;verticalAlign=middle;align=center;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="850" y="65" width="700" height="25" as="geometry"/>
        </mxCell>

        <!-- 数据源层 -->
        <mxCell id="data-sources-layer" value="数据源层 (Multi-Source Data Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="120" width="2700" height="200" as="geometry"/>
        </mxCell>

        <!-- 汽车行业数据源 -->
        <mxCell id="auto-sources" value="汽车行业数据源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;fontSize=12;fontStyle=1;" vertex="1" parent="data-sources-layer">
          <mxGeometry x="30" y="40" width="400" height="140" as="geometry"/>
        </mxCell>
        <mxCell id="auto-4s" value="4S店反馈系统\n• 销售反馈\n• 售后服务\n• 客户投诉" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" vertex="1" parent="auto-sources">
          <mxGeometry x="10" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="auto-app" value="车主APP\n• 用户评价\n• 功能反馈\n• 使用体验" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" vertex="1" parent="auto-sources">
          <mxGeometry x="110" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="auto-hotline" value="客服热线\n• 电话投诉\n• 咨询记录\n• 处理结果" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" vertex="1" parent="auto-sources">
          <mxGeometry x="210" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="auto-social" value="社交媒体\n• 微博评论\n• 论坛讨论\n• 短视频评价" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=10;" vertex="1" parent="auto-sources">
          <mxGeometry x="310" y="30" width="90" height="80" as="geometry"/>
        </mxCell>

        <!-- 星巴克数据源 -->
        <mxCell id="starbucks-sources" value="星巴克数据源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=12;fontStyle=1;" vertex="1" parent="data-sources-layer">
          <mxGeometry x="450" y="40" width="400" height="140" as="geometry"/>
        </mxCell>
        <mxCell id="sb-store" value="门店体验\n• 服务评价\n• 环境反馈\n• 产品质量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="starbucks-sources">
          <mxGeometry x="10" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="sb-app" value="官方APP\n• 订单评价\n• 功能建议\n• 配送体验" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="starbucks-sources">
          <mxGeometry x="110" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="sb-social" value="社交平台\n• 小红书\n• 抖音评论\n• 微信群聊" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="starbucks-sources">
          <mxGeometry x="210" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="sb-third" value="第三方平台\n• 美团评价\n• 大众点评\n• 饿了么反馈" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="starbucks-sources">
          <mxGeometry x="310" y="30" width="90" height="80" as="geometry"/>
        </mxCell>

        <!-- 信访数据源 -->
        <mxCell id="petition-sources" value="信访数据源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;fontStyle=1;" vertex="1" parent="data-sources-layer">
          <mxGeometry x="870" y="40" width="400" height="140" as="geometry"/>
        </mxCell>
        <mxCell id="petition-platform" value="政府平台\n• 网上信访\n• 政务服务\n• 民生热线" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57c00;fontSize=10;" vertex="1" parent="petition-sources">
          <mxGeometry x="10" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="petition-hotline" value="热线电话\n• 12345热线\n• 专线投诉\n• 紧急求助" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57c00;fontSize=10;" vertex="1" parent="petition-sources">
          <mxGeometry x="110" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="petition-visit" value="现场接访\n• 窗口服务\n• 现场投诉\n• 面对面沟通" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57c00;fontSize=10;" vertex="1" parent="petition-sources">
          <mxGeometry x="210" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="petition-online" value="网络渠道\n• 官方网站\n• 微信公众号\n• 在线留言" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57c00;fontSize=10;" vertex="1" parent="petition-sources">
          <mxGeometry x="310" y="30" width="90" height="80" as="geometry"/>
        </mxCell>

        <!-- 扩展行业数据源 -->
        <mxCell id="extend-sources" value="扩展行业数据源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;fontStyle=1;" vertex="1" parent="data-sources-layer">
          <mxGeometry x="1290" y="40" width="400" height="140" as="geometry"/>
        </mxCell>
        <mxCell id="mobile-sources" value="手机行业\n• 电商平台\n• 官方商城\n• 用户论坛" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="extend-sources">
          <mxGeometry x="10" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="beauty-sources" value="美妆行业\n• 小红书\n• 抖音直播\n• 电商评价" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="extend-sources">
          <mxGeometry x="110" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="other-sources" value="其他行业\n• 配置化接入\n• 标准化适配\n• 快速扩展" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="extend-sources">
          <mxGeometry x="210" y="30" width="90" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="future-sources" value="未来扩展\n• AI自动识别\n• 智能适配\n• 零配置接入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#7b1fa2;fontSize=10;" vertex="1" parent="extend-sources">
          <mxGeometry x="310" y="30" width="90" height="80" as="geometry"/>
        </mxCell>

        <!-- 数据格式说明 -->
        <mxCell id="format-info" value="支持数据格式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00695c;fontSize=12;fontStyle=1;" vertex="1" parent="data-sources-layer">
          <mxGeometry x="1710" y="40" width="300" height="140" as="geometry"/>
        </mxCell>
        <mxCell id="format-list" value="• CSV/Excel文件\n• JSON/XML数据\n• 数据库表(MySQL/PostgreSQL)\n• API接口数据\n• 消息队列(Kafka)\n• 实时流数据\n• 日志文件\n• 网页爬虫数据" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;fontSize=10;" vertex="1" parent="format-info">
          <mxGeometry x="10" y="30" width="280" height="100" as="geometry"/>
        </mxCell>

        <!-- 数据接入与预处理层 -->
        <mxCell id="ingestion-layer" value="数据接入与预处理层 (Data Ingestion & Pre-processing Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="340" width="2700" height="280" as="geometry"/>
        </mxCell>

        <!-- 数据采集器 -->
        <mxCell id="data-collectors" value="数据采集器集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=12;fontStyle=1;" vertex="1" parent="ingestion-layer">
          <mxGeometry x="30" y="40" width="500" height="220" as="geometry"/>
        </mxCell>
        <mxCell id="file-collector" value="文件采集器\n• 批量文件上传\n• FTP/SFTP采集\n• 定时文件扫描\n• 格式自动识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="data-collectors">
          <mxGeometry x="20" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="api-collector" value="API采集器\n• RESTful API\n• GraphQL接口\n• 第三方平台API\n• 认证与限流" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="data-collectors">
          <mxGeometry x="140" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="db-collector" value="数据库采集器\n• CDC变更捕获\n• 增量同步\n• 全量抽取\n• 多数据源支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="data-collectors">
          <mxGeometry x="260" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="stream-collector" value="流数据采集器\n• Kafka消费者\n• 实时数据流\n• 消息队列\n• 背压控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="data-collectors">
          <mxGeometry x="380" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="collector-config" value="采集器配置管理\n• 动态配置更新\n• 采集频率控制\n• 错误重试机制\n• 监控告警" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dcedc8;strokeColor=#689f38;fontSize=10;" vertex="1" parent="data-collectors">
          <mxGeometry x="20" y="120" width="470" height="80" as="geometry"/>
        </mxCell>

        <!-- 数据验证与清洗 -->
        <mxCell id="validation-cleaning" value="数据验证与清洗引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;fontStyle=1;" vertex="1" parent="ingestion-layer">
          <mxGeometry x="550" y="40" width="500" height="220" as="geometry"/>
        </mxCell>
        <mxCell id="schema-validation" value="模式验证器\n• 字段类型检查\n• 必填字段验证\n• 格式规范检查\n• 业务规则验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f9a825;fontSize=10;" vertex="1" parent="validation-cleaning">
          <mxGeometry x="20" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="data-cleaner" value="数据清洗器\n• 去重处理\n• 空值处理\n• 异常值检测\n• 数据标准化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f9a825;fontSize=10;" vertex="1" parent="validation-cleaning">
          <mxGeometry x="140" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="text-processor" value="文本预处理器\n• 编码转换\n• 特殊字符处理\n• 敏感信息脱敏\n• 文本分词" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f9a825;fontSize=10;" vertex="1" parent="validation-cleaning">
          <mxGeometry x="260" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="quality-checker" value="质量检查器\n• 完整性检查\n• 一致性验证\n• 准确性评估\n• 质量评分" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f9a825;fontSize=10;" vertex="1" parent="validation-cleaning">
          <mxGeometry x="380" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="error-handler" value="错误处理机制\n• 错误分类标记\n• 脏数据隔离\n• 重试机制\n• 人工审核队列" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffecb3;strokeColor=#ffa000;fontSize=10;" vertex="1" parent="validation-cleaning">
          <mxGeometry x="20" y="120" width="470" height="80" as="geometry"/>
        </mxCell>

        <!-- 配置管理与路由 -->
        <mxCell id="config-routing" value="配置管理与智能路由" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;fontStyle=1;" vertex="1" parent="ingestion-layer">
          <mxGeometry x="1070" y="40" width="500" height="220" as="geometry"/>
        </mxCell>
        <mxCell id="industry-config" value="行业配置管理\n• 字段映射规则\n• 验证规则配置\n• 清洗规则定义\n• 业务规则设置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#ad1457;fontSize=10;" vertex="1" parent="config-routing">
          <mxGeometry x="20" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="smart-router" value="智能路由器\n• 数据源识别\n• 行业自动分类\n• 处理路径选择\n• 负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#ad1457;fontSize=10;" vertex="1" parent="config-routing">
          <mxGeometry x="140" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="ai-classifier" value="AI分类器\n• 内容智能分类\n• 行业自动识别\n• 数据类型判断\n• 优先级评估" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#ad1457;fontSize=10;" vertex="1" parent="config-routing">
          <mxGeometry x="260" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="config-cache" value="配置缓存\n• 热配置缓存\n• 规则快速查询\n• 配置版本管理\n• 动态更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#ad1457;fontSize=10;" vertex="1" parent="config-routing">
          <mxGeometry x="380" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="routing-monitor" value="路由监控与优化\n• 处理性能监控\n• 路由效率分析\n• 瓶颈识别\n• 自动优化建议" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;fontSize=10;" vertex="1" parent="config-routing">
          <mxGeometry x="20" y="120" width="470" height="80" as="geometry"/>
        </mxCell>

        <!-- 消息队列与缓冲 -->
        <mxCell id="message-queue" value="消息队列与数据缓冲" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00695c;fontSize=12;fontStyle=1;" vertex="1" parent="ingestion-layer">
          <mxGeometry x="1590" y="40" width="500" height="220" as="geometry"/>
        </mxCell>
        <mxCell id="raw-queue" value="原始数据队列\n• Kafka Topic\n• 分区策略\n• 消息持久化\n• 高吞吐量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="message-queue">
          <mxGeometry x="20" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="clean-queue" value="清洗数据队列\n• 验证后数据\n• 标准化格式\n• 质量标记\n• 路由信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="message-queue">
          <mxGeometry x="140" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="error-queue" value="错误数据队列\n• 验证失败数据\n• 错误原因标记\n• 重试机制\n• 人工处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="message-queue">
          <mxGeometry x="260" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="priority-queue" value="优先级队列\n• 紧急数据优先\n• VIP客户优先\n• 实时处理\n• SLA保证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;fontSize=10;" vertex="1" parent="message-queue">
          <mxGeometry x="380" y="30" width="110" height="80" as="geometry"/>
        </mxCell>
        <mxCell id="queue-monitor" value="队列监控与管理\n• 消息积压监控\n• 消费速率控制\n• 死信队列处理\n• 性能调优" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="message-queue">
          <mxGeometry x="20" y="120" width="470" height="80" as="geometry"/>
        </mxCell>

        <!-- 数据接入层连接线 -->
        <mxCell id="source-to-collector" edge="1" parent="1" source="data-sources-layer" target="data-collectors" style="endArrow=block;strokeWidth=3;strokeColor=#2e7d32;">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="collector-to-validation" edge="1" parent="1" source="data-collectors" target="validation-cleaning" style="endArrow=block;strokeWidth=3;strokeColor=#f57c00;">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="validation-to-config" edge="1" parent="1" source="validation-cleaning" target="config-routing" style="endArrow=block;strokeWidth=2;strokeColor=#7b1fa2;">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="config-to-queue" edge="1" parent="1" source="config-routing" target="message-queue" style="endArrow=block;strokeWidth=3;strokeColor=#00695c;">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
